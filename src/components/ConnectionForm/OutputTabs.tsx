
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import {
  OutputForm,
  techs,
  energySources,
  materials,
  units,
  EnergyOutput,
  MaterialOutput,
  TechnologyResponse,
  MaterialResponse,
  EnergyResponse,
  EmissionResponse
} from "./types";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Plus, Trash } from "lucide-react";
import { filterValidNodes, getNodeLabel } from "@/utils/dropdownUtils";

interface OutputTabsProps {
  output: OutputForm;
  errors: Record<string, string>;
  updateOutputField: (fieldPath: string, value: any) => void;
  availableNodes: any[];
  clearError?: (errorKey: string) => void; // Add function to clear specific errors
  readOnly?: boolean;
  // API data props
  apiTechnologies?: TechnologyResponse[];
  apiMaterials?: MaterialResponse[];
  apiEnergies?: EnergyResponse[];
  apiEmissions?: EmissionResponse[];
  isLoadingApiData?: boolean;
}

export const OutputTabs: React.FC<OutputTabsProps> = ({
  output,
  errors,
  updateOutputField,
  availableNodes,
  clearError,
  readOnly = false,
  apiTechnologies = [],
  apiMaterials = [],
  apiEnergies = [],
  apiEmissions = [],
  isLoadingApiData = false
}) => {

  // Get data from API or fallback to hardcoded arrays
  const availableTechnologies = apiTechnologies.length > 0
    ? apiTechnologies.map(tech => tech.name).filter(name => name && name.trim() !== "")
    : techs.filter(tech => tech && tech.trim() !== "");

  const availableMaterials = apiMaterials.length > 0
    ? apiMaterials.map(material => material.name).filter(name => name && name.trim() !== "")
    : materials.filter(material => material && material.trim() !== "");

  const availableEnergies = apiEnergies.length > 0
    ? apiEnergies.map(energy => energy.name).filter(name => name && name.trim() !== "")
    : energySources.filter(energy => energy && energy.trim() !== "");

  // Helper function to convert node ID to human-readable activity name
  const getActivityNameFromId = (nodeId: string): string => {
    if (!nodeId || nodeId === "Nil") return "Nil";

    // Find the node in availableNodes
    const node = availableNodes.find(n => n.id === nodeId);
    if (node) {
      return getNodeLabel(node);
    }

    // If not found, return the ID as fallback (this handles the case where it's already a name)
    return nodeId;
  };

  // State for creating a new activity
  const [showNewActivityDialog, setShowNewActivityDialog] = useState(false);
  const [newActivityName, setNewActivityName] = useState("");

  // Removed local state - using parent state directly to avoid conflicts
  

  
  // Get validation errors for specific outputs
  const getEnergyOutputError = (energyOutputId: string) => {
    return errors[`${output.id}.energyOutputs.${energyOutputId}.energy`];
  };

  const getMaterialOutputError = (matOutputId: string) => {
    return errors[`${output.id}.matOutputs.${matOutputId}.material`];
  };

  // Get validation errors for destination/connect fields
  const getEnergyConnectError = (energyOutputId: string) => {
    return errors[`${output.id}.energyOutputs.${energyOutputId}.connect`];
  };

  const getMaterialConnectError = (matOutputId: string) => {
    return errors[`${output.id}.matOutputs.${matOutputId}.connect`];
  };

  // Filter out nodes that don't have valid IDs and remove "input1" option
  const validNodes = availableNodes
    .filter(node => node.id && node.id.trim() !== "" && node.id !== "input1");

  // Ensure arrays exist with fallbacks
  const energyOutputs = output.energyOutputs || [];
  const matOutputs = output.matOutputs || [];







  // New utility functions to handle multiple outputs
  const addEnergyOutput = () => {
    const currentOutputs = output.energyOutputs || [];
    const newId = `energy-${Date.now()}-${currentOutputs.length}`;
    const newEnergyOutput: EnergyOutput = {
      id: newId,
      energy: "",
      unit: "GJ",
      sec: "",
      final: false,
      connect: "",
      qty: "",
      qtyUnit: "GJ",
      destinationTechnology: ""
    };

    // No local state to update - using parent state directly

    updateOutputField('energyOutputs', [...currentOutputs, newEnergyOutput]);
  };
  
  const addMaterialOutput = () => {
    const currentOutputs = output.matOutputs || [];
    const newId = `material-${Date.now()}-${currentOutputs.length}`;
    const newMaterialOutput: MaterialOutput = {
      id: newId,
      material: "",
      unit: "Tonnes",
      smc: "",
      final: false,
      connect: "",
      qty: "",
      qtyUnit: "Tonnes",
      destinationTechnology: ""
    };

    // No local state to update - using parent state directly

    updateOutputField('matOutputs', [...currentOutputs, newMaterialOutput]);
  };
  
  const removeEnergyOutput = (id: string) => {
    const currentOutputs = output.energyOutputs || [];
    // Don't allow removing the last energy output
    if (currentOutputs.length <= 1) return;

    const updatedOutputs = currentOutputs.filter(item => item.id !== id);
    updateOutputField('energyOutputs', updatedOutputs);

    // No local state to update - using parent state directly
  };
  
  const removeMaterialOutput = (id: string) => {
    const currentOutputs = output.matOutputs || [];
    // Don't allow removing the last material output
    if (currentOutputs.length <= 1) return;

    const updatedOutputs = currentOutputs.filter(item => item.id !== id);
    updateOutputField('matOutputs', updatedOutputs);

    // No local state to update - using parent state directly
  };
  
  // Simplified checkbox handlers that update parent state directly
  const handleEnergyFinalChange = (energyId: string, checked: boolean | "indeterminate") => {
    console.log(`=== ENERGY FINAL CHANGE ===`);
    console.log(`Energy output ${energyId} final state changing to:`, checked);

    // Convert to boolean
    const newState = checked === true;

    // Clear validation error immediately if marking as final
    if (newState && clearError) {
      const errorKey = `${output.id}.energyOutputs.${energyId}.connect`;
      clearError(errorKey);
    }

    // Update parent state directly with null check
    const currentOutputs = output.energyOutputs || [];
    const updatedOutputs = currentOutputs.map(item => {
      if (item.id === energyId) {
        return {
          ...item,
          final: newState
        };
      }
      return item;
    });

    console.log(`Updated energy outputs:`, updatedOutputs);
    console.log(`Calling updateOutputField with:`, 'energyOutputs', updatedOutputs);
    updateOutputField('energyOutputs', updatedOutputs);
  };
  
  const handleMaterialFinalChange = (materialId: string, checked: boolean | "indeterminate") => {
    console.log(`=== MATERIAL FINAL CHANGE ===`);
    console.log(`Material output ${materialId} final state changing to:`, checked);

    // Convert to boolean
    const newState = checked === true;

    // Clear validation error immediately if marking as final
    if (newState && clearError) {
      const errorKey = `${output.id}.matOutputs.${materialId}.connect`;
      clearError(errorKey);
    }

    // Update parent state directly with null check
    const currentOutputs = output.matOutputs || [];
    const updatedOutputs = currentOutputs.map(item => {
      if (item.id === materialId) {
        return {
          ...item,
          final: newState
        };
      }
      return item;
    });

    console.log(`Updated material outputs:`, updatedOutputs);
    updateOutputField('matOutputs', updatedOutputs);
  };
  
  // Update individual energy output field
  const updateEnergyOutputField = (id: string, field: string, value: any) => {
    console.log(`Updating energy field: ${field} to:`, value);

    const currentOutputs = output.energyOutputs || [];
    const updatedOutputs = currentOutputs.map(item => {
      if (item.id === id) {
        return { ...item, [field]: value };
      }
      return item;
    });

    updateOutputField('energyOutputs', updatedOutputs);
  };
  
  // Update individual material output field
  const updateMaterialOutputField = (id: string, field: string, value: any) => {
    console.log(`Updating material field: ${field} to:`, value);

    const currentOutputs = output.matOutputs || [];
    const updatedOutputs = currentOutputs.map(item => {
      if (item.id === id) {
        return { ...item, [field]: value };
      }
      return item;
    });

    updateOutputField('matOutputs', updatedOutputs);
  };
  

  
  // Function to handle new activity selection
  const handleNewActivitySelect = () => {
    setShowNewActivityDialog(true);
  };
  
  // Effect to monitor targetNode value and show dialog when "create_new" is selected
  useEffect(() => {
    if (output.targetNode === "create_new") {
      setShowNewActivityDialog(true);
    }
  }, [output.targetNode]);
  
  // Function to create a new activity node
  const handleCreateNewActivity = () => {
    if (newActivityName.trim() === "") return;
    
    // Create a unique ID for the new node
    const newNodeId = `node-${Date.now()}`;
    
    // Create a new node object
    const newNode = {
      id: newNodeId,
      data: { label: newActivityName.trim() },
      position: { x: 0, y: 0 }, // Position will be adjusted by the parent component
      type: 'custom',
    };
    
    // Set the target node ID in the current output
    updateOutputField('targetNode', newNodeId);
    
    // Add the new node to the diagram via a custom event
    const event = new CustomEvent('add-new-activity-node', {
      detail: {
        node: newNode,
        sourceNodeId: output.id.split('-')[1], // Extract the source node ID from the output ID
      }
    });
    document.dispatchEvent(event);
    
    // Reset state and close dialog
    setNewActivityName("");
    setShowNewActivityDialog(false);
  };

  // Close dialog and reset selection if canceled
  const handleDialogClose = () => {
    setShowNewActivityDialog(false);
    // Reset the targetNode selection if it was "create_new"
    if (output.targetNode === "create_new") {
      updateOutputField('targetNode', '');
    }
  };

  // Render destination activity and technology selection
  const renderDestinationSelect = (type: 'energy' | 'material', id: string, data: EnergyOutput | MaterialOutput) => {
    const connectError = type === 'energy' ? getEnergyConnectError(id) : getMaterialConnectError(id);

    return (
      <div className="mt-2 bg-gray-50 p-4 rounded-md">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Destination Activity</label>
            {readOnly ? (
              <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                {getActivityNameFromId(data.connect || "")}
              </div>
            ) : (
              <Select
                value={data.connect || ""}
                onValueChange={(value) => {
                  if (type === 'energy') {
                    updateEnergyOutputField(id, 'connect', value);
                  } else {
                    updateMaterialOutputField(id, 'connect', value);
                  }

                  // We no longer update the parent targetNode
                  // Each output's connect property is now independent
                }}
              >
                <SelectTrigger className={`w-full ${connectError ? 'border-red-500' : ''}`}>
                  <SelectValue placeholder="Select destination" />
                </SelectTrigger>
                <SelectContent>
                  {validNodes.length > 0 ? (
                    <>
                      {validNodes.map(node => (
                        <SelectItem key={node.id} value={node.id}>
                          {node.data?.label || node.id || "Unnamed node"}
                        </SelectItem>
                      ))}
                      <SelectItem value="create_new">+ Create new activity</SelectItem>
                    </>
                  ) : (
                    <>
                      <SelectItem value="no-nodes-available" disabled>No destinations available</SelectItem>
                      <SelectItem value="create_new">+ Create new activity</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            )}
            {connectError && (
              <p className="text-red-500 text-xs mt-1">{connectError}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Select technology</label>
            {readOnly ? (
              <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                {data.destinationTechnology || "Select technology"}
              </div>
            ) : (
              <Select
                value={data.destinationTechnology || undefined}
                onValueChange={(value) => {
                  if (type === 'energy') {
                    updateEnergyOutputField(id, 'destinationTechnology', value);
                  } else {
                    updateMaterialOutputField(id, 'destinationTechnology', value);
                  }
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select technology" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingApiData ? (
                    <SelectItem value="loading" disabled>Loading technologies...</SelectItem>
                  ) : (
                    availableTechnologies.map(tech => (
                      <SelectItem key={tech} value={tech}>{tech}</SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            )}
          </div>
        </div>
      </div>
    );
  };
  
  // Render final output quantity fields
  const renderFinalOutputFields = (type: 'energy' | 'material', id: string, data: EnergyOutput | MaterialOutput) => (
    <div className="mt-2 bg-gray-50 p-4 rounded-md">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Final output Quantity</label>
          <Input 
            type="text"
            placeholder="50"
            value={data.qty || ""} 
            onChange={(e) => {
              if (type === 'energy') {
                updateEnergyOutputField(id, 'qty', e.target.value);
              } else {
                updateMaterialOutputField(id, 'qty', e.target.value);
              }
            }} 
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
          {readOnly ? (
            <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
              {data.qtyUnit || (type === 'energy' ? "GJ" : "Tonnes")}
            </div>
          ) : (
            <Select
              value={data.qtyUnit || (type === 'energy' ? "GJ" : "Tonnes")}
              onValueChange={(value) => {
                if (type === 'energy') {
                  updateEnergyOutputField(id, 'qtyUnit', value);
                } else {
                  updateMaterialOutputField(id, 'qtyUnit', value);
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={type === 'energy' ? "GJ" : "Tonnes"} />
              </SelectTrigger>
              <SelectContent>
                {units.map(unit => (
                  <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Primary Energy Output Section */}
      <div className="bg-white rounded-md mb-3 p-4 border border-gray-200">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-medium text-gray-700">Primary energy Output</h3>
          {!readOnly && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="text-blue-600 flex items-center text-sm"
              onClick={addEnergyOutput}
            >
              <Plus className="mr-1 h-3 w-3" /> Energy output
            </Button>
          )}
        </div>
        
        {(output.energyOutputs || []).map((energyOutput, index) => {
          // Use parent state directly
          const isFinal = !!energyOutput.final;
            
          return (
            <div 
              key={energyOutput.id} 
              className={`mb-6 p-4 border rounded-md ${
                getEnergyOutputError(energyOutput.id) ? 'border-red-300' : 'border-gray-200'
              } ${index > 0 ? 'mt-4' : ''}`}
            >
              {/* Output header with remove button */}
              {(output.energyOutputs || []).length > 1 && (
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-sm text-gray-500">Energy output {index + 1}</span>
                  {!readOnly && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeEnergyOutput(energyOutput.id)}
                      className="h-8 w-8 p-0"
                    >
                      <Trash className="h-4 w-4 text-red-500" />
                    </Button>
                  )}
                </div>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                  {readOnly ? (
                    <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                      {energyOutput.energy || "Select energy"}
                    </div>
                  ) : (
                    <Select
                      value={energyOutput.energy || undefined}
                      onValueChange={(value) => updateEnergyOutputField(energyOutput.id, 'energy', value)}
                    >
                      <SelectTrigger className={`w-full ${getEnergyOutputError(energyOutput.id) ? 'border-red-500' : ''}`}>
                        <SelectValue placeholder="Select energy" />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingApiData ? (
                          <SelectItem value="loading" disabled>Loading energies...</SelectItem>
                        ) : (
                          availableEnergies.map(energy => (
                            <SelectItem key={energy} value={energy}>{energy}</SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  )}
                  {getEnergyOutputError(energyOutput.id) && (
                    <p className="text-red-500 text-xs mt-1">{getEnergyOutputError(energyOutput.id)}</p>
                  )}
                </div>
  
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
                  {readOnly ? (
                    <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                      {energyOutput.unit || "GJ"}
                    </div>
                  ) : (
                    <Select
                      value={energyOutput.unit || "GJ"}
                      onValueChange={(value) => updateEnergyOutputField(energyOutput.id, 'unit', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="GJ" />
                      </SelectTrigger>
                      <SelectContent>
                        {units.map(unit => (
                          <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
  
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">SEC</label>
                  <Input 
                    type="text"
                    placeholder="0.4"
                    value={energyOutput.sec || ""} 
                    onChange={(e) => updateEnergyOutputField(energyOutput.id, 'sec', e.target.value)} 
                  />
                </div>
              </div>
  
              {/* Simple test checkbox that bypasses all complex state management */}
              <div className="flex items-center gap-2 mt-1 bg-gray-50 p-4 rounded-md">
                <div className="flex items-center space-x-2 relative z-10">
                  <input
                    type="checkbox"
                    id={`energy-final-${energyOutput.id}`}
                    checked={isFinal}
                    disabled={readOnly}
                    onChange={readOnly ? undefined : (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log(`=== SIMPLE CHECKBOX TEST ===`);
                      console.log(`Checkbox clicked, current checked:`, e.target.checked);
                      console.log(`Current energyOutput:`, energyOutput);

                      // Try direct state modification
                      energyOutput.final = e.target.checked;
                      console.log(`Updated energyOutput:`, energyOutput);

                      // Force a re-render by updating the parent
                      updateOutputField('energyOutputs', [...(output.energyOutputs || [])]);
                    }}
                    className={readOnly ? "cursor-not-allowed opacity-50" : "cursor-pointer"}
                  />
                  <label
                    htmlFor={`energy-final-${energyOutput.id}`}
                    className="text-sm font-medium leading-none cursor-pointer"
                  >
                    Final output
                  </label>
                </div>
              </div>
              
              {/* Conditional rendering based on checkbox state */}
              {isFinal ? 
                renderFinalOutputFields('energy', energyOutput.id, energyOutput) : 
                renderDestinationSelect('energy', energyOutput.id, energyOutput)
              }
            </div>
          );
        })}
      </div>

      {/* Primary Material Output Section */}
      <div className="bg-white rounded-md mb-3 p-4 border border-gray-200">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-medium text-gray-700">Primary material Output</h3>
          {!readOnly && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="text-blue-600 flex items-center text-sm"
              onClick={addMaterialOutput}
            >
              <Plus className="mr-1 h-3 w-3" /> Material Output
            </Button>
          )}
        </div>
        
        {(output.matOutputs || []).map((matOutput, index) => {
          // Use parent state directly
          const isFinal = !!matOutput.final;
            
          return (
            <div 
              key={matOutput.id} 
              className={`mb-6 p-4 border rounded-md ${
                getMaterialOutputError(matOutput.id) ? 'border-red-300' : 'border-gray-200'
              } ${index > 0 ? 'mt-4' : ''}`}
            >
              {/* Output header with remove button */}
              {(output.matOutputs || []).length > 1 && (
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-sm text-gray-500">Material output {index + 1}</span>
                  {!readOnly && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeMaterialOutput(matOutput.id)}
                      className="h-8 w-8 p-0"
                    >
                      <Trash className="h-4 w-4 text-red-500" />
                    </Button>
                  )}
                </div>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                  {readOnly ? (
                    <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                      {matOutput.material || "Select material"}
                    </div>
                  ) : (
                    <Select
                      value={matOutput.material || undefined}
                      onValueChange={(value) => updateMaterialOutputField(matOutput.id, 'material', value)}
                    >
                      <SelectTrigger className={`w-full ${getMaterialOutputError(matOutput.id) ? 'border-red-500' : ''}`}>
                        <SelectValue placeholder="Select material" />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingApiData ? (
                          <SelectItem value="loading" disabled>Loading materials...</SelectItem>
                        ) : (
                          availableMaterials.map(material => (
                            <SelectItem key={material} value={material}>{material}</SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  )}
                  {getMaterialOutputError(matOutput.id) && (
                    <p className="text-red-500 text-xs mt-1">{getMaterialOutputError(matOutput.id)}</p>
                  )}
                </div>
  
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
                  {readOnly ? (
                    <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                      {matOutput.unit || "Tonnes"}
                    </div>
                  ) : (
                    <Select
                      value={matOutput.unit || "Tonnes"}
                      onValueChange={(value) => updateMaterialOutputField(matOutput.id, 'unit', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Tonnes" />
                      </SelectTrigger>
                      <SelectContent>
                        {units.map(unit => (
                          <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
  
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">SMC</label>
                  <Input 
                    type="text"
                    placeholder="0.4"
                    value={matOutput.smc || ""} 
                    onChange={(e) => updateMaterialOutputField(matOutput.id, 'smc', e.target.value)} 
                  />
                </div>
              </div>
  
              {/* Simple test checkbox for material that bypasses complex state management */}
              <div className="flex items-center gap-2 bg-gray-50 p-4 rounded-md">
                <div className="flex items-center space-x-2 relative z-10">
                  <input
                    type="checkbox"
                    id={`mat-final-${matOutput.id}`}
                    checked={isFinal}
                    disabled={readOnly}
                    onChange={readOnly ? undefined : (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log(`=== SIMPLE MATERIAL CHECKBOX TEST ===`);
                      console.log(`Material checkbox clicked, current checked:`, e.target.checked);
                      console.log(`Current matOutput:`, matOutput);

                      // Try direct state modification
                      matOutput.final = e.target.checked;
                      console.log(`Updated matOutput:`, matOutput);

                      // Force a re-render by updating the parent
                      updateOutputField('matOutputs', [...(output.matOutputs || [])]);
                    }}
                    className={readOnly ? "cursor-not-allowed opacity-50" : "cursor-pointer"}
                  />
                  <label
                    htmlFor={`mat-final-${matOutput.id}`}
                    className="text-sm font-medium leading-none cursor-pointer"
                  >
                    Final output
                  </label>
                </div>
              </div>
              
              {/* Conditional rendering based on checkbox state */}
              {isFinal ? 
                renderFinalOutputFields('material', matOutput.id, matOutput) : 
                renderDestinationSelect('material', matOutput.id, matOutput)
              }
            </div>
          );
        })}
      </div>
      
      {/* Dialog for creating a new activity */}
      <Dialog open={showNewActivityDialog} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Activity</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="activity-name" className="text-right col-span-1">
                Activity Name
              </label>
              <Input
                id="activity-name"
                value={newActivityName}
                onChange={(e) => setNewActivityName(e.target.value)}
                placeholder="Enter activity name"
                className="col-span-3"
                autoFocus
                autoComplete="off"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleDialogClose}>
              Cancel
            </Button>
            <Button 
              type="button" 
              onClick={handleCreateNewActivity}
              disabled={newActivityName.trim() === ""}
            >
              Create
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
